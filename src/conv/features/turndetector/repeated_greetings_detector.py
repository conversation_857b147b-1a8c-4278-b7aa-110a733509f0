import time
from collections import deque

from config import load_config, get_config_value
from .turn_feature_detector import TurnFeatureDetector


class RepeatedGreetingsDetector(TurnFeatureDetector):
    def __init__(self, greet_window_sec=2.0, greet_max_repeats=2):
        self.greet_window_sec = greet_window_sec or get_config_value('constants',
                                                                     'turn_detection.default_greet_window_sec', 2.0)
        self.greet_max_repeats = greet_max_repeats or get_config_value('constants',
                                                                       'turn_detection.default_greet_max_repeats', 2)
        self._recent_greets = deque()
        
        # Load greeting phrases from configuration
        messages_config = load_config('messages')
        self.greet_set = set(messages_config.get('greeting_phrases', []))

    def check(self, chat_ctx):
        texts = [m.text_content.lower().strip() for m in chat_ctx.items if
                 hasattr(m, "role") and m.role == "user" and m.text_content]
        if not texts:
            return False
        last = texts[-1]
        now = time.time()
        if last not in self.greet_set:
            self._recent_greets.clear()
            return False
        self._recent_greets.append((now, last))
        while self._recent_greets and now - self._recent_greets[0][0] > self.greet_window_sec:
            self._recent_greets.popleft()
        if len(self._recent_greets) >= self.greet_max_repeats:
            return True
        return False
