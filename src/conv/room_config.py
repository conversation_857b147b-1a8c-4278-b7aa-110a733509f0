import json
from typing import Optional, List

from pydantic import BaseModel, Field

from conv.conv_meta import MetadataType, LLMSettings, VoiceConfig, ConversationContext, AnalysisSettings, FunctionType


class RoomConfig(BaseModel):
    type: str = Field(
        default=str(MetadataType.TEST_OUTBOUND_CAMPAIGN_CALL),
        description="Type of metadata",
    )
    llm: LLMSettings = Field(default_factory=LLMSettings, description="LLM configuration")
    voice: VoiceConfig = Field(default_factory=VoiceConfig, description="TTS configuration")
    context: ConversationContext = Field(default_factory=ConversationContext, description="Contextual information")
    analysis: Optional[AnalysisSettings] = Field(
        default_factory=AnalysisSettings,
        description="Analysis configuration"
    )
    functions: List[FunctionType] = Field(
        default_factory=lambda: ['schedule_callback', 'end_conversation', 'donot_call'],
        description="List of enabled functions for the conversation"
    )
    is_closed: Optional[bool] = Field(default=False, description="Is session closed")

    def model_post_init(self, *args, **kwargs):
        # Ensure mandatory functions are always present
        mandatory_functions = {'schedule_callback', 'end_conversation',
                               'donot_call'}  # {"schedule_callback", "donot_call"}
        functions_set = set(self.functions)
        functions_set.update(mandatory_functions)
        self.functions = list(functions_set)

    @classmethod
    def close_session(cls):
        cls.is_closed = True

    def is_session_closed(self):
        return self.is_closed

    @classmethod
    def load_json(cls, data: str) -> 'RoomConfig':
        parsed_data = json.loads(data)
        return cls.load_dict(parsed_data)

    @classmethod
    def load_dict(cls, data: dict) -> 'RoomConfig':
        # Use factory for voice settings
        if "voice" in data:
            data["voice"] = VoiceConfig.factory(data["voice"])
        return cls.model_validate(data)
