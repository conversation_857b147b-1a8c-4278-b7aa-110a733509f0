import logging
from typing import Optional

import sentry_sdk
from livekit.agents import metrics, MetricsCollectedEvent, JobContext
from livekit.agents.metrics import UsageCollector

from log.sentry import sentry_span

_logger = logging.getLogger(__name__)


class UsageCollector:
    """
    Manages usage metrics collection and logging for conversation sessions.
    This class encapsulates all usage collection functionality and provides
    a clean interface for tracking and reporting usage metrics.
    """

    def __init__(self, ctx: JobContext):
        """
        Initialize the usage collection manager.
        
        Args:
            ctx: The JobContext for registering callbacks
        """
        self.ctx = ctx
        self.usage_collector: UsageCollector = metrics.UsageCollector()
        self._metrics_callback_registered = False
        _logger.debug("UsageCollectionCollector initialized")

    def setup_metrics_collection(self, session):
        """
        Set up metrics collection for the given session.
        This method registers the metrics collection callback with the session.
        
        Args:
            session: The AgentSession to collect metrics from
        """
        if self._metrics_callback_registered:
            _logger.warning("Metrics collection already set up, skipping")
            return

        @session.on("metrics_collected")
        def _on_metrics_collected(ev: MetricsCollectedEvent):
            """Handle metrics collection events from the session."""
            try:
                self.usage_collector.collect(ev.metrics)
            #  _logger.debug(f"Collected metrics: {ev.metrics}")
            except Exception as e:
                _logger.error(f"Error collecting metrics: {e}")
                sentry_sdk.capture_exception(e)

        self._metrics_callback_registered = True
        _logger.info("Metrics collection callback registered with session")

    @sentry_span(op="usage.log", description="log usage summary")
    async def log_usage_summary(self):
        """
        Log the current usage summary.
        This method is typically called during shutdown to report final usage.
        """
        try:
            summary = self.usage_collector.get_summary()
            _logger.warning(f"Usage Summary: {summary}")

            # Also send to Sentry for monitoring
            span = sentry_sdk.get_current_span()
            if span:
                span.set_data("usage_summary", summary)

        except Exception as e:
            _logger.error(f"Error logging usage summary: {e}")
            sentry_sdk.capture_exception(e)

    def register_shutdown_logging(self):
        """
        Register the usage logging callback with the JobContext.
        This ensures usage is logged when the system shuts down.
        """
        self.ctx.add_shutdown_callback(self.log_usage_summary)
        _logger.debug("Usage logging callback registered with JobContext")

    def get_current_summary(self) -> Optional[dict]:
        """
        Get the current usage summary without logging it.
        
        Returns:
            The current usage summary or None if an error occurs
        """
        try:
            return self.usage_collector.get_summary()
        except Exception as e:
            _logger.error(f"Error getting usage summary: {e}")
            return None

    def reset_metrics(self):
        """
        Reset the usage collector to start fresh metrics collection.
        This can be useful for per-conversation metrics tracking.
        """
        self.usage_collector = metrics.UsageCollector()
        self._metrics_callback_registered = False
        _logger.debug("Usage metrics reset")
