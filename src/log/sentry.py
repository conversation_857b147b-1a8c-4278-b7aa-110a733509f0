import asyncio
import functools
import logging
from typing import Callable, Any, TypeVar
from typing import Optional

import sentry_sdk
from sentry_sdk.integrations.asyncio import AsyncioIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

F = TypeVar('F', bound=Callable[..., Any])


def initialize_sentry(config):
   sentry_environment = config.app.sentry_environment if config.app.sentry_environment else config.env
   sentry_sdk.init(
        dsn=config.app.sentry_dsn,
        environment=sentry_environment,
        integrations=[
            LoggingIntegration(
                level=logging.ERROR,
                event_level=logging.ERROR
            ),
            AsyncioIntegration(),
        ],
        traces_sample_rate=1.0, # if sentry_environment != "production" else 0.1,
        profiles_sample_rate=1.0 # if sentry_environment != "production" else 0.1,
    )

def capture_errors(f):
    @functools.wraps(f)
    async def wrapper(*args, **kwargs):
        try:
            return await f(*args, **kwargs)
        except Exception as e:
            logger = logging.getLogger(f.__module__)
            logger.error(f"Exception in {f.__name__}: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)
            raise
    return wrapper


def sentry_span(op: Optional[str] = None, description: Optional[str] = None) -> Callable[[F], F]:
    def decorator(func: F) -> F:
        is_coroutine = asyncio.iscoroutinefunction(func)

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            span_op = op if op else f"{func.__module__}.{func.__name__}"
            span_description = description if description else func.__name__
            with sentry_sdk.start_span(op=span_op, name=span_description) as span:
                try:
                    result = await func(*args, **kwargs)
                    span.set_status("ok")
                    span.finish()
                    return result
                except Exception as e:
                    span.set_status("error")
                    span.finish()
                    sentry_sdk.capture_exception(e)
                    raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            span_op = op if op else f"{func.__module__}.{func.__name__}"
            span_description = description if description else func.__name__
            with sentry_sdk.start_span(op=span_op, name=span_description) as span:
                try:
                    result = func(*args, **kwargs)
                    span.set_status("ok")
                    span.finish()
                    return result
                except Exception as e:
                    span.set_status("error")
                    sentry_sdk.capture_exception(e)
                    span.finish()
                    raise

        return async_wrapper if is_coroutine else sync_wrapper  # type: ignore

    return decorator


def sentry_transaction(op: Optional[str] = None, name: Optional[str] = None) -> Callable[[F], F]:
    def decorator(func: F) -> F:
        is_coroutine = asyncio.iscoroutinefunction(func)

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            transaction_op = op if op else f"{func.__module__}.{func.__name__}"
            transaction_name = name if name else func.__name__
            with sentry_sdk.start_transaction(op=transaction_op, name=transaction_name) as transaction:
                try:
                    result = await func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("error")
                    sentry_sdk.capture_exception(e)
                    raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            transaction_op = op if op else f"{func.__module__}.{func.__name__}"
            transaction_name = name if name else func.__name__
            with sentry_sdk.start_transaction(op=transaction_op, name=transaction_name) as transaction:
                try:
                    result = func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("error")
                    sentry_sdk.capture_exception(e)
                    raise

        return async_wrapper if is_coroutine else sync_wrapper  # type: ignore

    return decorator
