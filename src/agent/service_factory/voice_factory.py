from livekit.plugins import elevenlabs

from app.config import get_config
from conv.conv_meta import VoiceConfig

config = get_config()


def create_tts(provider: str = "11labs", voice_info: VoiceConfig = None):
    if provider.lower() == "playai":
        from livekit.plugins import playai
        return playai.TTS(
            voice=voice_info.voiceId,
            model=voice_info.model
        )
    elif provider.lower() == "11labs":
        return elevenlabs.TTS(
            api_key=config.elevenlabs.api_key,
            voice_id=voice_info.voiceId,
            voice_settings=elevenlabs.tts.VoiceSettings(
                voice_info.stability,
                voice_info.similarity_boost,
                voice_info.style,
                voice_info.use_speaker_boost
            ),
            streaming_latency=voice_info.streaming_latency
        )
    else:
        raise ValueError(f"Unsupported TTS provider: {provider}")
    # elif provider.lower() == "google":
    #     try:
    #         credentials = get_google_credentials()
    #         google_languages = {
    #             'eng': 'en-US',
    #             'ara': 'ar-XA',
    #             'ukr': 'uk-UA',
    #             'th': 'th-TH'
    #         }
    #
    #         language = 'en-US'
    #         if agent_settings and agent_settings.languages in google_languages:
    #             language = google_languages[agent_settings.languages]
    #         from livekit.plugins import google
    #         return google.TTS(
    #             language=language,
    #             gender="neutral",
    #             encoding="linear16",
    #             sample_rate=16000,
    #             speaking_rate=1.0,
    #             credentials_file=credentials
    #         )
    #     except ValueError as e:
    #         raise ValueError(f"Failed to initialize Google TTS: {str(e)}")

#
# def get_google_credentials():
#     """Get Google credentials from the configured path."""
#     credentials_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
#                                     'speech-api-credentials.json')
#     try:
#         return credentials_path
#     except FileNotFoundError:
#         raise ValueError(f"Google Speech API credentials file not found at {credentials_path}")
#     except json.JSONDecodeError:
#         raise ValueError(f"Invalid JSON in Google Speech API credentials file at {credentials_path}")
