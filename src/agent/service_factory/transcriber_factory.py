import os

from livekit.plugins import deepgram, openai

from agent.language_config import LanguageSettings
from app.config import get_config

config = get_config()
lang_settings = LanguageSettings()

def create_stt(language: str = "eng", streaming: bool = False):
    locale = lang_settings.get_locale(language)

    if config.app.stt_provider.lower() == "openai" or language == 'ukr' or language == 'rus' or language == 'arb':
        return openai.STT(detect_language=True, model='gpt-4o-mini-transcribe', use_realtime=True)

    if config.app.stt_provider.lower() == "nemo":
        from serve.asr.nemo.model_server import NemoModel  # Updated import
        from serve.asr.nemo.nemo_stt import NemoSTT, NemoSTTOptions

        if streaming:
            return NemoSTT(
                opts=NemoSTTOptions(
                    model=NemoModel.CONFORMER_CTC if language == "eng" else NemoModel.QUARTZNET_ARABIC,
                    language=language
                )
            )
    elif config.app.stt_provider.lower() == "whisper":
        from serve.infra.whisper_stt import WhiSTT
        return WhiSTT(base_url=config.app.stt_url, language=language)

    elif config.app.stt_provider.lower() == "google":
        from livekit.plugins import google
        try:
            credentials_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                            'speech-api-credentials.json')
            return google.STT(
                location='eu',
                languages=[locale],
                detect_language=True,
                model='long',
                interim_results=False,
                punctuate=False,
                credentials_file=credentials_path
            )
        except ValueError as e:
            raise ValueError(f"Failed to initialize Google STT: {str(e)}")

    elif config.app.stt_provider.lower() == "assemblyai":
        from livekit.plugins import assemblyai
        lang_name, _ = lang_settings.map_language_code(language)
        return assemblyai.STT(
            api_key=config.assemblyai.api_key,
            # speech_model='Nano',
            # language=lang_name.lower()
        )
    elif config.app.stt_provider.lower() == "openai" or language == 'ukr' or language == 'ara':
        return openai.STT(language=language, detect_language=True, model='gpt-4o-mini-transcribe', use_realtime=True)
    else:  # default to deepgram
        lang_name, _ = lang_settings.map_language_code(language)
        return deepgram.STT(
            language=locale,
	        api_key=config.deepgram.api_key,
            model=config.deepgram.model,
        )